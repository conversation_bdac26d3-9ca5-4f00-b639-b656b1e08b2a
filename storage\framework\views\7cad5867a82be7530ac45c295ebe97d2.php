<?php $__env->startSection('meta_tags'); ?>
    

<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Start or Stop Break')); ?>

<?php $__env->startSection('css_links'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
        /* Custom CSS Here */
        * {
        user-select: none; /* Standard syntax */
        -webkit-user-select: none; /* Chrome, Safari */
        -moz-user-select: none; /* Firefox */
        -ms-user-select: none; /* IE10+ */
        }

        .custom-option-content {
            position: relative;
        }
        .custom-option-content .form-check-input {
            position: absolute;
            top: 10px;
            right: 10px;
        }
    </style>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('Start or Stop Break')); ?></b>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><?php echo e(__('Daily Break')); ?></li>
    <li class="breadcrumb-item active"><?php echo e(__('Start or Stop Break')); ?></li>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<!-- Start row -->
<div class="row justify-content-center">
    <?php if(isset($activeBreak)): ?>
        <div class="col-md-7">
            <div class="card mb-4">
                <div class="card-header header-elements">
                    <h5 class="mb-0 text-bold">Running Break</h5>
                    <?php if(isset($activeBreak->break_in_at)): ?>
                        <small class="badge bg-dark fs-6 ms-auto"
                            id="runningBreakTime"
                            data-break-in-at="<?php echo e($activeBreak->break_in_at->timestamp); ?>"
                            title="Running Break"
                            style="margin-top: -5px;">
                        </small>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <dl class="row mb-1">
                        <dt class="col-sm-4 mb-2 fw-medium text-nowrap">
                            <i class="ti ti-access-point text-heading"></i>
                            <span class="fw-medium mx-2 text-heading">Break Type:</span>
                        </dt>
                        <dd class="col-sm-8">
                            <?php if($activeBreak->type == 'Short'): ?>
                                <span class="badge bg-primary text-bold"><?php echo e(__('Short Break')); ?></span>
                            <?php else: ?>
                                <span class="badge bg-warning text-bold"><?php echo e(__('Long Break')); ?></span>
                            <?php endif; ?>
                        </dd>
                    </dl>
                    <dl class="row mb-1">
                        <dt class="col-sm-4 mb-2 fw-medium text-nowrap">
                            <i class="ti ti-clock-play text-heading"></i>
                            <span class="fw-medium mx-2 text-heading">Started At:</span>
                        </dt>
                        <dd class="col-sm-8">
                            <span class="text-bold text-primary"><?php echo e(show_time($activeBreak->break_in_at)); ?></span>
                        </dd>
                    </dl>
                    <dl class="row mb-1">
                        <dt class="col-sm-4 mb-2 fw-medium text-nowrap">
                            <i class="ti ti-access-point text-heading"></i>
                            <span class="fw-medium mx-2 text-heading">Break Start IP:</span>
                        </dt>
                        <dd class="col-sm-8">
                            <span><?php echo e($activeBreak->break_in_ip); ?></span>
                        </dd>
                    </dl>
                </div>

                <div class="card-footer">
                    <div class="text-end">
                        <form action="<?php echo e(route('administration.daily_break.stop')); ?>" method="post" class="confirm-form-danger">
                            <?php echo csrf_field(); ?>
                            <button type="submit" name="stop_break" class="btn btn-danger">
                                <span class="tf-icon ti ti-clock-stop me-1"></span>
                                <?php echo e(__('Stop Break')); ?>

                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="col-md-7">
            <form action="<?php echo e(route('administration.daily_break.start')); ?>" method="POST" autocomplete="off">
                <?php echo csrf_field(); ?>
                <div class="card mb-4">
                    <div class="card-header header-elements">
                        <h5 class="mb-0">Start Daily Break</h5>
                    </div>
                    <div class="card-body">
                        <input type="hidden" name="userid" value="<?php echo e(auth()->user()->userid); ?>" required>
                        <div class="mb-3 col-md-12">
                            <div class="row">
                                <div class="col-md mb-md-0 mb-2">
                                    <div class="form-check custom-option custom-option-icon form-check-primary bg-label-primary">
                                        <label class="form-check-label custom-option-content" for="shortBreak">
                                            <span class="custom-option-body">
                                                <img src="<?php echo e(asset('assets/img/illustrations/page-misc-launching-soon.png')); ?>" width="92" class="mb-2">
                                                <span class="h6 mb-0 text-uppercase text-bold custom-option-title">Short Break</span>
                                                <small>
                                                    <span>You Can Take Maximum <b>Two Short Breaks</b></span>
                                                    <br>
                                                    <span>For <b>15-20 Min</b></span>
                                                </small>
                                            </span>
                                            <input name="break_type" value="Short" class="form-check-input" type="radio" id="shortBreak" required />
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md mb-md-0 mb-2">
                                    <div class="form-check custom-option custom-option-icon form-check-warning bg-label-warning">
                                        <label class="form-check-label custom-option-content" for="longBreak">
                                            <span class="custom-option-body">
                                                <img src="<?php echo e(asset('assets/img/illustrations/page-misc-you-are-not-authorized.png')); ?>" width="60" class="mb-2">
                                                <span class="h6 mb-0 text-uppercase text-bold custom-option-title">Long Break</span>
                                                <small>
                                                    <span>You Can Take Maximum <b>One Long Break</b></span>
                                                    <br>
                                                    <span>For <b>30-45 Min</b></span>
                                                </small>
                                            </span>
                                            <input name="break_type" value="Long" class="form-check-input" type="radio" id="longBreak" required />
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="text-end">
                            <button type="submit" class="btn btn-primary btn-break-start" onclick="this.disabled=true; this.form.submit();">
                                <span class="tf-icon ti ti-check ti-xs me-1"></span>
                                Start Break
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    <?php endif; ?>

    <?php if($breaks): ?>
        <div class="col-md-5">
            <div class="card">
                <div class="card-header header-elements">
                    <h5 class="mb-0">Start Daily Break</h5>
                    <?php if(isset($attendance)): ?>
                        <div class="ms-auto" style="margin-top: -5px;">
                            <?php if(isset($attendance->total_break_time)): ?>
                                <small class="badge bg-dark" title="Total Break Taken">
                                    <?php echo e(total_time($attendance->total_break_time)); ?>

                                </small>
                            <?php endif; ?>
                            <?php if(isset($attendance->total_over_break)): ?>
                                <small class="badge bg-danger" title="Total Over Break">
                                    <?php echo e(total_time($attendance->total_over_break)); ?>

                                </small>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <ul class="timeline mb-0 pb-1">
                        <?php $__empty_1 = true; $__currentLoopData = $breaks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $break): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <li class="timeline-item ps-4 <?php echo e($loop->last ? 'border-transparent' : 'border-left-dashed pb-1'); ?>">
                                <span class="timeline-indicator-advanced timeline-indicator-<?php echo e($break->type == 'Short' ? 'primary' : 'warning'); ?>">
                                    <i class="ti ti-<?php echo e($break->break_out_at ? 'clock-stop' : 'clock-play'); ?>"></i>
                                </span>
                                <div class="timeline-event px-0 pb-0">
                                    <div class="timeline-header">
                                        <small class="text-uppercase fw-medium" title="Click To See Details">
                                            <a href="<?php echo e(route('administration.daily_break.show', ['break' => $break])); ?>" class="text-<?php echo e($break->type == 'Short' ? 'primary' : 'warning'); ?>"><?php echo e($break->type); ?> Break</a>
                                        </small>
                                    </div>
                                    <small class="text-muted mb-0">
                                        <?php echo e(show_time($break->break_in_at)); ?>

                                        <?php if(!is_null($break->break_out_at)): ?>
                                            <span>to</span>
                                            <span><?php echo e(show_time($break->break_out_at)); ?></span>
                                        <?php else: ?>
                                            -
                                            <span class="text-danger">Break Running</span>
                                        <?php endif; ?>
                                    </small>
                                    <h6 class="mb-1">
                                        <?php if(is_null($break->total_time)): ?>
                                            <span class="text-danger">Break Running</span>
                                        <?php else: ?>
                                            <span class="text-<?php echo e($break->type == 'Short' ? 'primary' : 'warning'); ?>"><?php echo e(total_time($break->total_time)); ?></span>
                                            <?php if(isset($break->over_break)): ?>
                                                <small class="text-danger text-bold mt-1" title="Over Break">(<?php echo e(total_time($break->over_break)); ?>)</small>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </h6>
                                </div>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="text-center text-bold text-muted fs-2">No Breaks</div>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>
<!-- End row -->

<?php $__env->stopSection(); ?>


<?php $__env->startSection('script_links'); ?>
    
    <script src="<?php echo e(asset('assets/js/form-layouts.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        $(document).ready(function() {
            const runningBreakTimeElement = $('#runningBreakTime');

            if (runningBreakTimeElement.length) {
                const breakInAt = parseInt(runningBreakTimeElement.data('break-in-at')) * 1000; // Convert to milliseconds

                // Function to calculate and display the elapsed time
                function updateRunningBreakTime() {
                    const now = new Date().getTime();
                    const elapsed = now - breakInAt;

                    // Calculate hours, minutes, and seconds
                    const hours = Math.floor(elapsed / (1000 * 60 * 60));
                    const minutes = Math.floor((elapsed % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((elapsed % (1000 * 60)) / 1000);

                    // Format the time as hh:mm:ss
                    const formattedTime =
                        String(hours).padStart(2, '0') + ':' +
                        String(minutes).padStart(2, '0') + ':' +
                        String(seconds).padStart(2, '0');

                    runningBreakTimeElement.text(formattedTime);
                }

                // Update the time every second
                updateRunningBreakTime(); // Initial call
                setInterval(updateRunningBreakTime, 1000); // Update every second
            }
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/daily_break/create.blade.php ENDPATH**/ ?>