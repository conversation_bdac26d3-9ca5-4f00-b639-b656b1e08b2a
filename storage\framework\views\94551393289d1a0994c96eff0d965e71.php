<?php $__env->startSection('meta_tags'); ?>
    

<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Barcode Attendance')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <!-- DataTables css -->
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/datatable.css')); ?>" rel="stylesheet" type="text/css" />

    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/select2/select2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
    /* Custom CSS Here */
    </style>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('Barcode Attendance')); ?></b>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><?php echo e(__('Attendance')); ?></li>
    <li class="breadcrumb-item active"><?php echo e(__('Barcode Attendance')); ?></li>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<!-- Start row -->
<div class="row justify-content-center">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header header-elements">
                <h5 class="mb-0">Barcode Attendance</h5>

                <div class="card-header-elements ms-auto">
                    <a href="<?php echo e(route('administration.attendance.barcode.scanner')); ?>" class="btn btn-sm btn-dark" title="Reload Page?">
                        <span class="tf-icon ti ti-reload ti-xs me-1"></span>
                        Reload
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form action="<?php echo e(route('administration.attendance.barcode.scan', ['scanner_id' => $scanner_id])); ?>" method="POST" autocomplete="off" id="barcodeScannerForm">
                    <?php echo csrf_field(); ?>
                    <div class="row">
                        <div class="mb-3 col-md-12">
                            <div class="row">
                                <div class="col-md mb-md-0 mb-2">
                                    <div class="form-check custom-option custom-option-basic form-check-primary">
                                        <label class="form-check-label custom-option-content" for="typeRegular">
                                            <input name="type" class="form-check-input" type="radio" value="Regular" id="typeRegular" checked />
                                            <span class="custom-option-header pb-0">
                                                <span class="h6 mb-0 text-primary text-bold"><?php echo e(__('Regular')); ?></span>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md">
                                    <div class="form-check custom-option custom-option-basic form-check-warning">
                                        <label class="form-check-label custom-option-content" for="typeOvertime">
                                            <input name="type" class="form-check-input" type="radio" value="Overtime" id="typeOvertime" />
                                            <span class="custom-option-header pb-0">
                                                <span class="h6 mb-0 text-warning text-bold"><?php echo e(__('Overtime')); ?></span>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-12">
                            <label class="form-label text-bold text-dark"><?php echo e(__('User ID')); ?> <strong class="text-danger">*</strong></label>
                            <div class="input-group input-group-merge">
                                <span class="input-group-text" style="padding-right: 2px;">UID</span>
                                <input type="text" id="userid" name="userid" class="form-control <?php $__errorArgs = ['userid'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="20010101" autofocus required/>
                            </div>
                            <?php $__errorArgs = ['userid'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header header-elements">
                <h5 class="mb-0">
                    <span>Attendances of last </span>
                    <span class="text-bold text-primary"><?php echo e($hours); ?> Hours</span>
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive-md table-responsive-sm w-100">
                    <table class="table data-table table-bordered">
                        <thead>
                            <tr>
                                <th>Sl.</th>
                                <th>Name</th>
                                <th>Clocked IN</th>
                                <th>Clock Out</th>
                                <th class="text-center">Breaks</th>
                                <th>Total</th>
                                <th>Scanned By</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $attendances; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $attendance): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr class="bg-label-<?php echo e($attendance->type === 'Regular' ? '' : 'warning'); ?>">
                                    <th class="text-center">
                                        #<?php echo e(serial($attendances, $key)); ?>

                                        <br>
                                        <small class="text-bold badge bg-<?php echo e($attendance->type === 'Regular' ? 'success' : 'warning'); ?>"><?php echo e($attendance->type); ?></small>
                                    </th>
                                    <td>
                                        <?php if($attendance->user): ?>
                                            <?php echo show_user_name_and_avatar($attendance->user, role: null); ?>

                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="d-grid">
                                            <?php
                                                if (get_time_only($attendance->clock_in) > $attendance->employee_shift->start_time){
                                                    $clockInColor = 'text-danger';
                                                } else {
                                                    $clockInColor = 'text-success';
                                                }
                                            ?>
                                            <span class="text-truncate text-bold <?php echo e($clockInColor); ?>"><?php echo e(show_time($attendance->clock_in)); ?></span>
                                            <small class="text-truncate text-muted" data-bs-toggle="tooltip" data-bs-placement="left" title="Shift Start Time"><?php echo e(show_time($attendance->employee_shift->start_time)); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-grid">
                                            <?php if(isset($attendance->clock_out)): ?>
                                                <?php
                                                    if (get_time_only($attendance->clock_out) < $attendance->employee_shift->end_time){
                                                        $clockOutColor = 'text-danger';
                                                    } else {
                                                        $clockOutColor = 'text-success';
                                                    }
                                                ?>
                                                <span class="text-truncate text-bold <?php echo e($clockOutColor); ?>"><?php echo e(show_time($attendance->clock_out)); ?></span>
                                            <?php else: ?>
                                                <b class="text-success text-uppercase">Running</b>
                                            <?php endif; ?>
                                            <small class="text-truncate text-muted" data-bs-toggle="tooltip" data-bs-placement="right" title="Shift End Time"><?php echo e(show_time($attendance->employee_shift->end_time)); ?></small>
                                        </div>
                                    </td>
                                    <td class="text-center <?php echo e($attendance->type == 'Overtime' ? 'not-allowed' : ''); ?>">
                                        <?php if($attendance->type == 'Regular'): ?>
                                            <div class="d-grid">
                                                <b class="text-truncate">
                                                    <span class="text-warning" title="Total Break Time">
                                                        <?php echo e(total_time($attendance->total_break_time)); ?>

                                                    </span>
                                                    <?php if(isset($attendance->total_over_break)): ?>
                                                        <small class="text-danger" title="Total Over Break">
                                                            (<?php echo e(total_time($attendance->total_over_break)); ?>)
                                                        </small>
                                                    <?php endif; ?>
                                                </b>
                                                <small class="text-truncate text-muted">
                                                    Breaks Taken: <?php echo e($attendance->total_breaks_taken); ?>

                                                </small>
                                            </div>
                                        <?php else: ?>
                                            <b class="text-muted">No Break</b>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="d-grid">
                                            <?php if(isset($attendance->total_adjusted_time)): ?>
                                                <?php
                                                    $totalWorkingHour = get_total_hour($attendance->employee_shift->start_time, $attendance->employee_shift->end_time);
                                                ?>
                                                <b title="Adjusted Total Time">
                                                    <?php if($attendance->type == 'Regular'): ?>
                                                        <?php echo total_time_with_min_hour($attendance->total_adjusted_time, $totalWorkingHour); ?>

                                                    <?php else: ?>
                                                        <b class="text-warning">
                                                            <?php echo e(total_time($attendance->total_adjusted_time ?? $attendance->total_time)); ?>

                                                        </b>
                                                    <?php endif; ?>
                                                </b>
                                                <small class="text-truncate text-muted" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Total Working Time">
                                                    <?php echo e(total_time($attendance->total_time)); ?>

                                                </small>
                                            <?php else: ?>
                                                <b class="text-success text-uppercase">Running</b>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-dark text-truncate">
                                            <b>Clock-In:</b>
                                            <span><?php echo e(optional($attendance->clockin_scanner)->name); ?></span>
                                        </span>
                                        <br>
                                        <span class="text-dark text-truncate">
                                            <b>Clock-Out:</b>
                                            <span><?php echo e(optional($attendance->clockout_scanner)->name); ?></span>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End row -->

<?php $__env->stopSection(); ?>


<?php $__env->startSection('script_links'); ?>
    
    <!-- Datatable js -->
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/jquery.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/dataTables.bootstrap4.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/datatable.js')); ?>"></script>

    <script src="<?php echo e(asset('assets/js/form-layouts.js')); ?>"></script>

    <script src="<?php echo e(asset('assets/vendor/libs/select2/select2.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        $(document).ready(function() {
            $('.bootstrap-select').each(function() {
                if (!$(this).data('bs.select')) { // Check if it's already initialized
                    $(this).selectpicker();
                }
            });
        });
    </script>
    <script>
        $(document).ready(function() {
            // When any radio button for clock-in type is selected
            $('input[name="type"]').on('change', function() {
                // Clear the User ID field and focus on it
                $('#userid').val('').focus();
            });
        });
    </script>


    <script>
$(document).ready(function () {
    const $input = $('#userid');
    const $form = $input.closest('form'); // automatically finds the form
    let barcode = '';
    let lastKeyTime = Date.now();
    const MAX_INTERVAL = 50; // Max time between keystrokes to qualify as barcode

    $input.on('keydown', function (e) {
        const now = Date.now();
        const timeDiff = now - lastKeyTime;

        if (timeDiff > MAX_INTERVAL) {
            barcode = '';
        }

        lastKeyTime = now;
        const key = e.key;

        if (/^[\w\d]$/.test(key)) {
            barcode += key;
        } else if (e.keyCode === 13) {
            // Enter key = end of barcode
            e.preventDefault(); // prevent manual submission
            $input.val(barcode); // populate input
            barcode = '';

            // Submit the form
            $form.submit();
        }

        // Prevent all manual input
        e.preventDefault();
    });

    // Block pasting or right-click
    $input.on('paste contextmenu', function (e) {
        e.preventDefault();
    });
});
</script>


<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/attendance/barcode_scanner.blade.php ENDPATH**/ ?>